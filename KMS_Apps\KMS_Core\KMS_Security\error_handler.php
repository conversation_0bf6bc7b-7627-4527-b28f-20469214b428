<?php
// Custom Exception Classes for Better Error Handling

class DatabaseConnectionException extends Exception {
    public function __construct($message = "Database connection failed", $code = 0, Exception $previous = null) {
        parent::__construct($message, $code, $previous);
    }
}

class DatabaseQueryException extends Exception {
    private $query;
    
    public function __construct($message, $query = '', $code = 0, Exception $previous = null) {
        $this->query = $query;
        parent::__construct($message, $code, $previous);
    }
    
    public function getQuery() {
        return $this->query;
    }
}

class APIException extends Exception {
    private $context;
    
    public function __construct($message, $context = [], $code = 0, Exception $previous = null) {
        $this->context = $context;
        parent::__construct($message, $code, $previous);
    }
    
    public function getContext() {
        return $this->context;
    }
}

class APIErrorHandler {
    public static function handleException(Exception $e, string $operation = 'Unknown'): array {
        $errorData = [
            'success' => false,
            'message' => $e->getMessage(),
            'operation' => $operation,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Add specific context based on exception type
        if ($e instanceof DatabaseQueryException) {
            $errorData['query'] = $e->getQuery();
        } elseif ($e instanceof APIException) {
            $errorData['context'] = $e->getContext();
        }
        
        // Log error for debugging (in production, you'd use proper logging)
        error_log("API Error [$operation]: " . $e->getMessage());
        
        return $errorData;
    }
    
    public static function validateRequired(array $data, array $required): array {
        $missing = [];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }
        return $missing;
    }
    
    public static function sanitizeOutput(array $data): array {
        return array_map(function($value) {
            if (is_string($value)) {
                return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            }
            return $value;
        }, $data);
    }
}