/* Logo Management Styles */
:root {
    /* Color Variables */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    --error-gradient: linear-gradient(135deg, #f44336 0%, #da190b 100%);
    --light-gradient: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --white-gradient: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    
    /* Shadow Variables */
    --shadow-light: 0 8px 25px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 15px 35px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.15);
    --text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    
    /* Border Radius Variables */
    --radius-small: 5px;
    --radius-medium: 10px;
    --radius-large: 15px;
    --radius-xl: 20px;
    --radius-pill: 25px;
    
    /* Spacing Variables */
    --spacing-xs: 8px;
    --spacing-sm: 15px;
    --spacing-md: 20px;
    --spacing-lg: 25px;
    --spacing-xl: 30px;
    
    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: var(--primary-gradient);
    min-height: 100vh;
    margin: 0;
    font-family: var(--font-family);
}

.header-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.back-btn {
    display: inline-block;
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 80px 20px 20px;
}

h1 {
    text-align: center;
    color: white;
    margin-bottom: 40px;
    font-size: 26px;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.error-message {
    background: var(--error-gradient);
    color: white;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-large);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    text-shadow: var(--text-shadow);
}

.success-message {
    background: var(--success-gradient);
    color: white;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-large);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    text-shadow: var(--text-shadow);
}

.logo-section {
    background: rgba(255, 191, 0, 0.95);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-medium);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.logo-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.logo-section h2 {
    color: #333;
    border-bottom: 3px solid transparent;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    font-size: 24px;
    font-weight: 600;
    position: relative;
}

.logo-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.logo-preview {
    text-align: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.logo-preview img {
    width: 180px;
    height: 180px;
    border: 3px solid #fff;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    object-fit: contain;
    background: white;
    transition: transform 0.3s ease;
}

.logo-preview img:hover {
    transform: scale(1.05);
}

.current-logo-info {
    margin-top: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    font-size: 14px;
    text-align: left;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.upload-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.form-group input[type="file"] {
    width: 100%;
    padding: 15px;
    border: 2px dashed #667eea;
    border-radius: 10px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.form-group input[type="file"]:hover {
    border-color: #764ba2;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.file-requirements {
    margin-top: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid rgba(33, 150, 243, 0.2);
    border-radius: 10px;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.file-requirements strong {
    color: #1565c0;
    font-size: 16px;
}

.file-requirements ul {
    margin: 12px 0 0 0;
    padding-left: 20px;
}

.file-requirements li {
    margin-bottom: 8px;
    color: #1976d2;
    line-height: 1.4;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.btn-danger {
    background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
    color: white;
    border: 2px solid transparent;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #da190b 0%, #c62828 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .logo-section {
        padding: 15px;
    }
    
    .logo-preview img {
        max-width: 150px;
        max-height: 150px;
    }
    
    .btn {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animation for smooth transitions */
.logo-section {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translate3d(0, 20px, 0); 
    }
    to { 
        opacity: 1; 
        transform: translate3d(0, 0, 0); 
    }
}

/* Performance optimization for hover effects */
.logo-section,
.btn,
.logo-preview img {
    will-change: transform;
}

.logo-section:not(:hover),
.btn:not(:hover),
.logo-preview img:not(:hover) {
    will-change: auto;
}