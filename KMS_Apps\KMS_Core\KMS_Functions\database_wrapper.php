<?php
require_once __DIR__ . '/../KMS_Security/error_handler.php';

class DatabaseWrapper {
    private $connection;
    
    public function __construct() {
        $this->connection = get_db_connection();
        if (!$this->connection) {
            throw new Exception('Database connection failed: ' . mysqli_connect_error());
        }
    }
    
    public function tableExists(string $tableName): bool {
        $result = mysqli_query($this->connection, "SHOW TABLES LIKE '$tableName'");
        return $result && mysqli_num_rows($result) > 0;
    }
    
    public function columnExists(string $tableName, string $columnName): bool {
        $result = mysqli_query($this->connection, "SHOW COLUMNS FROM $tableName LIKE '$columnName'");
        return $result && mysqli_num_rows($result) > 0;
    }
    
    public function executeQuery(string $sql, array $params = []): array {
        try {
            if (empty($params)) {
                $result = mysqli_query($this->connection, $sql);
            } else {
                $stmt = mysqli_prepare($this->connection, $sql);
                if (!$stmt) {
                    throw new Exception('Prepare failed: ' . mysqli_error($this->connection));
                }
                
                if (!empty($params)) {
                    $types = str_repeat('s', count($params)); // Default to string
                    mysqli_stmt_bind_param($stmt, $types, ...$params);
                }
                
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                mysqli_stmt_close($stmt);
            }
            
            if (!$result) {
                throw new Exception('Query failed: ' . mysqli_error($this->connection));
            }
            
            $data = [];
            while ($row = mysqli_fetch_assoc($result)) {
                $data[] = $row;
            }
            
            return $data;
            
        } catch (Exception $e) {
            return APIErrorHandler::handleException($e, 'Database query');
        }
    }
    
    public function __destruct() {
        if ($this->connection) {
            close_db_connection($this->connection);
        }
    }
}