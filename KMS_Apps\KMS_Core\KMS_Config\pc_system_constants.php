<?php
// PC System Constants
class PCSystemConstants {
    // Table names
    public const TABLES = [
        'USERS' => 'users',
        'PC_ORDERS' => 'pc_orders',
        'PC_COMPONENTS' => 'pc_components',
        'PC_PREBUILT_CONFIGS' => 'pc_prebuilt_configs',
        'PC_COMPONENT_CATEGORIES' => 'pc_component_categories'
    ];
    
    // Order statuses
    public const ORDER_STATUSES = [
        'PENDING' => 'pending',
        'QUOTED' => 'quoted',
        'CONFIRMED' => 'confirmed',
        'PROCESSING' => 'processing',
        'COMPLETED' => 'completed',
        'CANCELLED' => 'cancelled',
        'REFUNDED' => 'refunded'
    ];
    
    // Payment statuses
    public const PAYMENT_STATUSES = [
        'PENDING' => 'pending',
        'PAID' => 'paid',
        'FAILED' => 'failed',
        'REFUNDED' => 'refunded'
    ];
    
    // Order types
    public const ORDER_TYPES = [
        'CUSTOM' => 'custom',
        'PREBUILT' => 'prebuilt'
    ];
    
    // API file paths
    public const API_PATHS = [
        'TEMP_API' => 'KMS_Apps/KMS_Member/KMS_PCBuilder/KMS_PHP/temp_pc_orders_api.php',
        'EXTENDED_API' => 'KMS_Apps/KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api_extended.php'
    ];
    
    // Status display styles
    public const STATUS_STYLES = [
        'success' => ['color' => 'green', 'bg' => '#d4edda', 'border' => '#c3e6cb', 'text' => '#155724'],
        'error' => ['color' => 'red', 'bg' => '#f8d7da', 'border' => '#f5c6cb', 'text' => '#721c24'],
        'warning' => ['color' => 'orange', 'bg' => '#fff3cd', 'border' => '#ffeaa7', 'text' => '#856404'],
        'info' => ['color' => 'blue', 'bg' => '#d1ecf1', 'border' => '#bee5eb', 'text' => '#0c5460']
    ];
    
    // Status icons
    public const STATUS_ICONS = [
        'success' => '✅',
        'error' => '❌',
        'warning' => '⚠️',
        'info' => 'ℹ️'
    ];
}