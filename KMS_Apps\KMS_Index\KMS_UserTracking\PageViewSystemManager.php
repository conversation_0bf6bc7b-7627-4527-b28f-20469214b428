<?php
/**
 * Page View System Manager
 * 統一管理頁面瀏覽量追蹤系統
 * 
 * 功能特點:
 * - 防重複計數（同一訪客每分鐘只計算一次）
 * - 支援已登入和未登入用戶
 * - 使用資料庫儲存，提供統計摘要
 * - 記錄 IP 地址和用戶代理（隱私保護）
 */

class PageViewSystemManager 
{
    // Class constants for better encapsulation
    public const DEFAULT_PAGE_NAME = 'index';
    public const DEFAULT_COOLDOWN_MINUTES = 1;
    public const DEFAULT_IP_ADDRESS = '127.0.0.1';
    public const DEFAULT_USER_AGENT = 'Unknown';
    
    private $db;
    private $cooldownMinutes;
    
    public function __construct($database_connection, $cooldown_minutes = self::DEFAULT_COOLDOWN_MINUTES) 
    {
        if (!$database_connection || !is_resource($database_connection) && !($database_connection instanceof mysqli)) {
            throw new InvalidArgumentException("Valid database connection required");
        }
        
        if (!is_int($cooldown_minutes) || $cooldown_minutes < 0) {
            throw new InvalidArgumentException("Cooldown minutes must be a non-negative integer");
        }
        
        $this->db = $database_connection;
        $this->cooldownMinutes = $cooldown_minutes;
    }
    
    /**
     * 記錄頁面瀏覽並返回總計數
     */
    public function recordViewAndGetCount($page_name = self::DEFAULT_PAGE_NAME) 
    {
        try {
            // 獲取訪客資訊
            $visitor_info = $this->getVisitorInfo();
            
            // 檢查是否為重複訪問
            if (!$this->isRecentView($page_name, $visitor_info)) {
                // 記錄新的瀏覽
                $this->insertPageView($page_name, $visitor_info);
                
                // 更新摘要表
                $this->updateSummary($page_name);
                
                error_log("New page view recorded: {$visitor_info['ip']} -> $page_name");
            } else {
                error_log("Duplicate view blocked: {$visitor_info['ip']} -> $page_name (within {$this->cooldownMinutes} minute(s))");
            }
            
            return $this->getTotalViews($page_name);
            
        } catch (Exception $e) {
            error_log("Error in recordViewAndGetCount: " . $e->getMessage());
            return $this->getTotalViews($page_name);
        }
    }
    
    /**
     * 獲取訪客資訊
     */
    private function getVisitorInfo() 
    {
        $user_id = null;
        if (isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true) {
            $user_id = intval($_SESSION["id"]);
        }
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? self::DEFAULT_IP_ADDRESS;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? self::DEFAULT_USER_AGENT;
        
        // 檢查是否有代理 IP
        if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $forwarded_ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $ip_address = trim($forwarded_ips[0]);
        } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
            $ip_address = $_SERVER['HTTP_X_REAL_IP'];
        }
        
        return [
            'user_id' => $user_id,
            'ip' => $ip_address,
            'user_agent' => $user_agent,
            'user_agent_hash' => hash('sha256', $user_agent),
            'session_id' => session_id()
        ];
    }
    
    /**
     * 檢查是否為最近的重複訪問
     */
    private function isRecentView($page_name, $visitor_info) 
    {
        $sql = "SELECT id FROM page_views 
                WHERE page_name = ? 
                AND ip_address = ? 
                AND user_agent_hash = ? 
                AND created_at > DATE_SUB(NOW(), INTERVAL ? MINUTE)
                LIMIT 1";
        
        $stmt = mysqli_prepare($this->db, $sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . mysqli_error($this->db));
        }
        
        mysqli_stmt_bind_param($stmt, "sssi", 
            $page_name, 
            $visitor_info['ip'], 
            $visitor_info['user_agent_hash'], 
            $this->cooldownMinutes
        );
        
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $has_recent = mysqli_num_rows($result) > 0;
        
        mysqli_stmt_close($stmt);
        return $has_recent;
    }
    
    /**
     * 插入新的頁面瀏覽記錄
     */
    private function insertPageView($page_name, $visitor_info) 
    {
        if ($visitor_info['user_id'] === null) {
            $sql = "INSERT INTO page_views (page_name, user_id, ip_address, user_agent_hash, session_id) 
                    VALUES (?, NULL, ?, ?, ?)";
            $stmt = mysqli_prepare($this->db, $sql);
            mysqli_stmt_bind_param($stmt, "ssss", 
                $page_name, 
                $visitor_info['ip'], 
                $visitor_info['user_agent_hash'], 
                $visitor_info['session_id']
            );
        } else {
            $sql = "INSERT INTO page_views (page_name, user_id, ip_address, user_agent_hash, session_id) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($this->db, $sql);
            mysqli_stmt_bind_param($stmt, "sisss", 
                $page_name, 
                $visitor_info['user_id'], 
                $visitor_info['ip'], 
                $visitor_info['user_agent_hash'], 
                $visitor_info['session_id']
            );
        }
        
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("Insert failed: " . mysqli_stmt_error($stmt));
        }
        
        mysqli_stmt_close($stmt);
    }
    
    /**
     * 更新摘要表
     */
    private function updateSummary($page_name) 
    {
        // 計算總瀏覽次數
        $total_sql = "SELECT COUNT(*) as total FROM page_views WHERE page_name = ?";
        $total_stmt = mysqli_prepare($this->db, $total_sql);
        mysqli_stmt_bind_param($total_stmt, "s", $page_name);
        mysqli_stmt_execute($total_stmt);
        $total_result = mysqli_stmt_get_result($total_stmt);
        $total_row = mysqli_fetch_assoc($total_result);
        $total_views = $total_row['total'];
        mysqli_stmt_close($total_stmt);
        
        // 計算獨立訪客數
        $unique_sql = "SELECT COUNT(DISTINCT CONCAT(ip_address, '-', user_agent_hash)) as unique_count 
                       FROM page_views WHERE page_name = ?";
        $unique_stmt = mysqli_prepare($this->db, $unique_sql);
        mysqli_stmt_bind_param($unique_stmt, "s", $page_name);
        mysqli_stmt_execute($unique_stmt);
        $unique_result = mysqli_stmt_get_result($unique_stmt);
        $unique_row = mysqli_fetch_assoc($unique_result);
        $unique_visitors = $unique_row['unique_count'];
        mysqli_stmt_close($unique_stmt);
        
        // 更新或插入摘要
        $update_sql = "INSERT INTO page_view_summary (page_name, total_views, unique_visitors) 
                       VALUES (?, ?, ?) 
                       ON DUPLICATE KEY UPDATE 
                       total_views = VALUES(total_views), 
                       unique_visitors = VALUES(unique_visitors),
                       last_updated = NOW()";
        
        $update_stmt = mysqli_prepare($this->db, $update_sql);
        mysqli_stmt_bind_param($update_stmt, "sii", $page_name, $total_views, $unique_visitors);
        
        if (!mysqli_stmt_execute($update_stmt)) {
            throw new Exception("Summary update failed: " . mysqli_stmt_error($update_stmt));
        }
        
        mysqli_stmt_close($update_stmt);
    }
    
    /**
     * 獲取總瀏覽次數
     */
    public function getTotalViews($page_name = self::DEFAULT_PAGE_NAME) 
    {
        // 優先從摘要表獲取（更快）
        $summary_sql = "SELECT total_views FROM page_view_summary WHERE page_name = ?";
        $stmt = mysqli_prepare($this->db, $summary_sql);
        mysqli_stmt_bind_param($stmt, "s", $page_name);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            return intval($row['total_views']);
        }
        
        mysqli_stmt_close($stmt);
        
        // 備用方案：直接從主表計算
        $count_sql = "SELECT COUNT(*) as total FROM page_views WHERE page_name = ?";
        $count_stmt = mysqli_prepare($this->db, $count_sql);
        mysqli_stmt_bind_param($count_stmt, "s", $page_name);
        mysqli_stmt_execute($count_stmt);
        $count_result = mysqli_stmt_get_result($count_stmt);
        
        if ($count_result) {
            $count_row = mysqli_fetch_assoc($count_result);
            mysqli_stmt_close($count_stmt);
            return intval($count_row['total']);
        }
        
        mysqli_stmt_close($count_stmt);
        return 0;
    }
    
    /**
     * 獲取統計資訊
     */
    public function getStats($page_name = self::DEFAULT_PAGE_NAME) 
    {
        $sql = "SELECT total_views, unique_visitors, last_updated 
                FROM page_view_summary 
                WHERE page_name = ?";
        
        $stmt = mysqli_prepare($this->db, $sql);
        mysqli_stmt_bind_param($stmt, "s", $page_name);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $stats = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            return $stats;
        }
        
        mysqli_stmt_close($stmt);
        return [
            'total_views' => 0,
            'unique_visitors' => 0,
            'last_updated' => null
        ];
    }
    
    /**
     * 初始化資料庫表
     */
    public function initializeTables() 
    {
        $tables = [
            'page_views' => "
                CREATE TABLE IF NOT EXISTS page_views (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    page_name VARCHAR(100) NOT NULL DEFAULT 'index',
                    user_id INT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent_hash VARCHAR(64) NOT NULL COMMENT 'SHA256 hash of user agent for privacy',
                    session_id VARCHAR(128) NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                    INDEX idx_page_name (page_name),
                    INDEX idx_ip_address (ip_address),
                    INDEX idx_user_agent_hash (user_agent_hash),
                    INDEX idx_created_at (created_at),
                    INDEX idx_user_id (user_id),
                    INDEX idx_duplicate_check (page_name, ip_address, user_agent_hash, created_at)
                )",
            
            'page_view_summary' => "
                CREATE TABLE IF NOT EXISTS page_view_summary (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    page_name VARCHAR(100) NOT NULL,
                    total_views INT NOT NULL DEFAULT 0,
                    unique_visitors INT NOT NULL DEFAULT 0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_page (page_name)
                )"
        ];
        
        foreach ($tables as $name => $sql) {
            if (!mysqli_query($this->db, $sql)) {
                throw new Exception("Failed to create table $name: " . mysqli_error($this->db));
            }
        }
        
        // 初始化 index 頁面記錄
        $init_sql = "INSERT INTO page_view_summary (page_name, total_views, unique_visitors) 
                     VALUES ('index', 0, 0)
                     ON DUPLICATE KEY UPDATE page_name = VALUES(page_name)";
        
        mysqli_query($this->db, $init_sql);
    }
}

// Global functions for backward compatibility (only define if not exists)
if (!function_exists('record_page_view_and_get_count')) {
    function record_page_view_and_get_count($connection = null) 
    {
        global $link;
        $db = $connection ?: $link;
        
        if (!$db) {
            error_log("No database connection available in record_page_view_and_get_count");
            return 0;
        }
        
        try {
            $manager = new PageViewSystemManager($db);
            return $manager->recordViewAndGetCount();
        } catch (Exception $e) {
            error_log("Error in record_page_view_and_get_count: " . $e->getMessage());
            return 0;
        }
    }
}

if (!function_exists('get_total_view_count')) {
    function get_total_view_count($page_name = PageViewSystemManager::DEFAULT_PAGE_NAME) 
    {
        global $link;
        
        if (!$link) {
            error_log("No database connection available in get_total_view_count");
            return 0;
        }
        
        try {
            $manager = new PageViewSystemManager($link);
            return $manager->getTotalViews($page_name);
        } catch (Exception $e) {
            error_log("Error in get_total_view_count: " . $e->getMessage());
            return 0;
        }
    }
}