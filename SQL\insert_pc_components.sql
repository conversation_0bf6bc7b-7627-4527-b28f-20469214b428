-- Insert PC Component Categories
INSERT INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, is_active, sort_order) VALUES
('CPU', 'CPU', 'CPU', 'Central Processing Unit', 1, 1),
('GPU', 'GPU', 'GPU', 'Graphics Processing Unit', 1, 2),
('RAM', 'RAM', 'RAM', 'Random Access Memory', 1, 3),
('Storage', 'Storage', 'Storage', 'Storage Devices', 1, 4),
('Case', 'Case', 'Case', 'PC Cases', 1, 5)
ON DUPLICATE KEY UPDATE 
    category_name = VALUES(category_name),
    description = VALUES(description),
    is_active = VALUES(is_active),
    sort_order = VALUES(sort_order);

-- Insert CPU Components
INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, base_price, current_price, stock_quantity, description, is_active, sort_order) VALUES
((SELECT id FROM pc_component_categories WHERE category_name = 'CPU'), 'Intel Core i9-285K', 'Intel Core i9-285K', 'Intel Core i9-285K', 'Intel', 'i9-285K', 760.00, 760.00, 10, 'High-performance CPU for gaming and content creation', 1, 1),
((SELECT id FROM pc_component_categories WHERE category_name = 'CPU'), 'Intel Core i5-265K', 'Intel Core i5-265K', 'Intel Core i5-265K', 'Intel', 'i5-265K', 380.00, 380.00, 15, 'Mid-range CPU for gaming', 1, 2),
((SELECT id FROM pc_component_categories WHERE category_name = 'CPU'), 'AMD Ryzen 7 7800X3D', 'AMD Ryzen 7 7800X3D', 'AMD Ryzen 7 7800X3D', 'AMD', '7800X3D', 481.00, 481.00, 12, 'Gaming-focused CPU with 3D V-Cache', 1, 3),
((SELECT id FROM pc_component_categories WHERE category_name = 'CPU'), 'AMD Ryzen 9 9800X3D', 'AMD Ryzen 9 9800X3D', 'AMD Ryzen 9 9800X3D', 'AMD', '9800X3D', 608.00, 608.00, 8, 'Latest gaming CPU with enhanced 3D V-Cache', 1, 4),
((SELECT id FROM pc_component_categories WHERE category_name = 'CPU'), 'AMD Ryzen 9 9950X3D', 'AMD Ryzen 9 9950X3D', 'AMD Ryzen 9 9950X3D', 'AMD', '9950X3D', 886.00, 886.00, 5, 'Flagship CPU for extreme performance', 1, 5)
ON DUPLICATE KEY UPDATE 
    component_name = VALUES(component_name),
    base_price = VALUES(base_price),
    current_price = VALUES(current_price),
    stock_quantity = VALUES(stock_quantity);

-- Insert GPU Components
INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, base_price, current_price, stock_quantity, description, is_active, sort_order) VALUES
((SELECT id FROM pc_component_categories WHERE category_name = 'GPU'), 'NVIDIA RTX 5070', 'NVIDIA RTX 5070', 'NVIDIA RTX 5070', 'NVIDIA', 'RTX 5070', 599.00, 599.00, 10, 'Mid-range graphics card for 1440p gaming', 1, 1),
((SELECT id FROM pc_component_categories WHERE category_name = 'GPU'), 'NVIDIA RTX 5070 Ti', 'NVIDIA RTX 5070 Ti', 'NVIDIA RTX 5070 Ti', 'NVIDIA', 'RTX 5070 Ti', 799.00, 799.00, 8, 'High-performance graphics card for 1440p/4K gaming', 1, 2),
((SELECT id FROM pc_component_categories WHERE category_name = 'GPU'), 'NVIDIA RTX 5080', 'NVIDIA RTX 5080', 'NVIDIA RTX 5080', 'NVIDIA', 'RTX 5080', 999.00, 999.00, 6, 'High-end graphics card for 4K gaming', 1, 3),
((SELECT id FROM pc_component_categories WHERE category_name = 'GPU'), 'NVIDIA RTX 5090', 'NVIDIA RTX 5090', 'NVIDIA RTX 5090', 'NVIDIA', 'RTX 5090', 1599.00, 1599.00, 4, 'Flagship graphics card for extreme 4K gaming', 1, 4)
ON DUPLICATE KEY UPDATE 
    component_name = VALUES(component_name),
    base_price = VALUES(base_price),
    current_price = VALUES(current_price),
    stock_quantity = VALUES(stock_quantity);

-- Insert RAM Components
INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, base_price, current_price, stock_quantity, description, is_active, sort_order) VALUES
((SELECT id FROM pc_component_categories WHERE category_name = 'RAM'), 'DDR5-6000 32GB Kit', 'DDR5-6000 32GB Kit', 'DDR5-6000 32GB Kit', 'Corsair', 'Vengeance', 299.00, 299.00, 20, '32GB DDR5 memory kit for gaming', 1, 1),
((SELECT id FROM pc_component_categories WHERE category_name = 'RAM'), 'DDR5-6000 64GB Kit', 'DDR5-6000 64GB Kit', 'DDR5-6000 64GB Kit', 'Corsair', 'Vengeance', 599.00, 599.00, 15, '64GB DDR5 memory kit for content creation', 1, 2),
((SELECT id FROM pc_component_categories WHERE category_name = 'RAM'), 'DDR5-6000 128GB Kit', 'DDR5-6000 128GB Kit', 'DDR5-6000 128GB Kit', 'G.Skill', 'Trident Z5', 1199.00, 1199.00, 8, '128GB DDR5 memory kit for professional workloads', 1, 3)
ON DUPLICATE KEY UPDATE 
    component_name = VALUES(component_name),
    base_price = VALUES(base_price),
    current_price = VALUES(current_price),
    stock_quantity = VALUES(stock_quantity);

-- Insert Storage Components
INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, base_price, current_price, stock_quantity, description, is_active, sort_order) VALUES
((SELECT id FROM pc_component_categories WHERE category_name = 'Storage'), 'NVMe SSD 2TB', 'NVMe SSD 2TB', 'NVMe SSD 2TB', 'Samsung', '990 Pro', 299.00, 299.00, 25, '2TB high-speed NVMe SSD', 1, 1),
((SELECT id FROM pc_component_categories WHERE category_name = 'Storage'), 'NVMe SSD 4TB', 'NVMe SSD 4TB', 'NVMe SSD 4TB', 'Samsung', '990 Pro', 599.00, 599.00, 20, '4TB high-speed NVMe SSD', 1, 2),
((SELECT id FROM pc_component_categories WHERE category_name = 'Storage'), 'NVMe SSD 8TB', 'NVMe SSD 8TB', 'NVMe SSD 8TB', 'Samsung', '990 Pro', 1199.00, 1199.00, 10, '8TB high-speed NVMe SSD', 1, 3)
ON DUPLICATE KEY UPDATE 
    component_name = VALUES(component_name),
    base_price = VALUES(base_price),
    current_price = VALUES(current_price),
    stock_quantity = VALUES(stock_quantity);

-- Insert Case Components
INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, base_price, current_price, stock_quantity, description, is_active, sort_order) VALUES
((SELECT id FROM pc_component_categories WHERE category_name = 'Case'), 'Small White Case', 'Small White Case', 'Small White Case', 'Fractal Design', 'Core 1000', 200.00, 200.00, 30, 'Compact white PC case', 1, 1),
((SELECT id FROM pc_component_categories WHERE category_name = 'Case'), 'Small Black Case', 'Small Black Case', 'Small Black Case', 'Fractal Design', 'Core 1000', 200.00, 200.00, 30, 'Compact black PC case', 1, 2),
((SELECT id FROM pc_component_categories WHERE category_name = 'Case'), 'Medium White Case', 'Medium White Case', 'Medium White Case', 'Fractal Design', 'Define R6', 200.00, 200.00, 25, 'Mid-tower white PC case', 1, 3),
((SELECT id FROM pc_component_categories WHERE category_name = 'Case'), 'Medium Black Case', 'Medium Black Case', 'Medium Black Case', 'Fractal Design', 'Define R6', 200.00, 200.00, 25, 'Mid-tower black PC case', 1, 4),
((SELECT id FROM pc_component_categories WHERE category_name = 'Case'), 'Large White Case', 'Large White Case', 'Large White Case', 'Fractal Design', 'Define 7 XL', 300.00, 300.00, 20, 'Full-tower white PC case', 1, 5),
((SELECT id FROM pc_component_categories WHERE category_name = 'Case'), 'Large Black Case', 'Large Black Case', 'Large Black Case', 'Fractal Design', 'Define 7 XL', 300.00, 300.00, 20, 'Full-tower black PC case', 1, 6)
ON DUPLICATE KEY UPDATE 
    component_name = VALUES(component_name),
    base_price = VALUES(base_price),
    current_price = VALUES(current_price),
    stock_quantity = VALUES(stock_quantity);

-- Insert PSU Components
INSERT INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, is_active, sort_order) VALUES
('PSU', 'PSU', 'PSU', 'Power Supply Unit', 1, 6)
ON DUPLICATE KEY UPDATE 
    category_name = VALUES(category_name),
    description = VALUES(description),
    is_active = VALUES(is_active),
    sort_order = VALUES(sort_order);

INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, base_price, current_price, stock_quantity, description, is_active, sort_order) VALUES
((SELECT id FROM pc_component_categories WHERE category_name = 'PSU'), '850W 80+ Gold PSU', '850W 80+ Gold PSU', '850W 80+ Gold PSU', 'Corsair', 'RM850x', 149.00, 149.00, 30, '850W modular power supply', 1, 1),
((SELECT id FROM pc_component_categories WHERE category_name = 'PSU'), '1000W 80+ Gold PSU', '1000W 80+ Gold PSU', '1000W 80+ Gold PSU', 'Corsair', 'RM1000x', 199.00, 199.00, 25, '1000W modular power supply', 1, 2),
((SELECT id FROM pc_component_categories WHERE category_name = 'PSU'), '1200W 80+ Platinum PSU', '1200W 80+ Platinum PSU', '1200W 80+ Platinum PSU', 'Corsair', 'AX1200i', 299.00, 299.00, 15, '1200W high-efficiency power supply', 1, 3)
ON DUPLICATE KEY UPDATE 
    component_name = VALUES(component_name),
    base_price = VALUES(base_price),
    current_price = VALUES(current_price),
    stock_quantity = VALUES(stock_quantity);

-- Insert Operating System Components
INSERT INTO pc_component_categories (category_name, category_name_en, category_name_zh, description, is_active, sort_order) VALUES
('OS', 'OS', 'OS', 'Operating System', 1, 7)
ON DUPLICATE KEY UPDATE 
    category_name = VALUES(category_name),
    description = VALUES(description),
    is_active = VALUES(is_active),
    sort_order = VALUES(sort_order);

INSERT INTO pc_components (category_id, component_name, component_name_en, component_name_zh, brand, model, base_price, current_price, stock_quantity, description, is_active, sort_order) VALUES
((SELECT id FROM pc_component_categories WHERE category_name = 'OS'), 'Windows 11 Home', 'Windows 11 Home', 'Windows 11 Home', 'Microsoft', 'Windows 11', 139.00, 139.00, 100, 'Windows 11 Home Edition', 1, 1),
((SELECT id FROM pc_component_categories WHERE category_name = 'OS'), 'Windows 11 Pro', 'Windows 11 Pro', 'Windows 11 Pro', 'Microsoft', 'Windows 11', 199.00, 199.00, 100, 'Windows 11 Professional Edition', 1, 2)
ON DUPLICATE KEY UPDATE 
    component_name = VALUES(component_name),
    base_price = VALUES(base_price),
    current_price = VALUES(current_price),
    stock_quantity = VALUES(stock_quantity);

-- Display results
SELECT 'PC components inserted successfully!' as message;
SELECT COUNT(*) as total_categories FROM pc_component_categories;
SELECT COUNT(*) as total_components FROM pc_components;
