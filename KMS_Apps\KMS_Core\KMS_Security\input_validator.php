<?php
class InputValidator {
    public static function validatePCOrderData(array $data): array {
        $errors = [];
        
        if (empty($data['order_type']) || !in_array($data['order_type'], ['custom', 'prebuilt'])) {
            $errors[] = 'Invalid order type';
        }
        
        if (isset($data['base_price']) && $data['base_price'] < 0) {
            $errors[] = 'Base price cannot be negative';
        }
        
        if (isset($data['components']) && !self::isValidJson($data['components'])) {
            $errors[] = 'Invalid components JSON format';
        }
        
        if (isset($data['configuration']) && !self::isValidJson($data['configuration'])) {
            $errors[] = 'Invalid configuration JSON format';
        }
        
        return $errors;
    }
    
    public static function validatePrebuiltConfig(array $data): array {
        $errors = [];
        
        if (empty($data['config_name'])) {
            $errors[] = 'Config name is required';
        }
        
        if (empty($data['tier'])) {
            $errors[] = 'Tier is required';
        }
        
        if (empty($data['primary_use'])) {
            $errors[] = 'Primary use is required';
        }
        
        if (isset($data['base_price']) && $data['base_price'] < 0) {
            $errors[] = 'Base price cannot be negative';
        }
        
        return $errors;
    }
    
    public static function sanitizeInput(string $input): string {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    public static function isValidJson(string $json): bool {
        json_decode($json);
        return json_last_error() === JSON_ERROR_NONE;
    }
    
    public static function validateEnum(string $value, array $allowedValues): bool {
        return in_array($value, $allowedValues, true);
    }
}