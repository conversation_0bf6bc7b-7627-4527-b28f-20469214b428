# KelvinKMS.com - 完整系統使用說明

## 系統概述

KelvinKMS.com 是一個多功能的網站系統，提供以下主要功能：
- 用戶註冊和登錄系統
- KMS Credit 錢包系統
- 照片/視頻優化服務
- 打印服務
- PC 組裝服務
- 推薦系統 (Affiliate System)
- 即時聊天系統
- 管理員後台管理

## 系統架構

### 目錄結構
```
KelvinKMS.com/
├── index.php                          # 主頁面
├── KMS_Apps/                          # 主要應用程序目錄
│   ├── KMS_Core/                      # 核心功能
│   │   ├── KMS_Config/                # 配置文件
│   │   ├── KMS_Functions/             # 通用函數
│   │   ├── KMS_Language/              # 多語言支持
│   │   └── KMS_Security/              # 安全功能
│   ├── KMS_Index/                     # 首頁相關功能
│   │   ├── KMS_Authentication/        # 登錄註冊
│   │   ├── KMS_Homepage/              # 主頁內容
│   │   ├── KMS_LiveChat/              # 即時聊天
│   │   └── KMS_UserTracking/          # 用戶追蹤
│   ├── KMS_Member/                    # 會員功能
│   │   ├── KMS_member.php             # 會員主頁面
│   │   ├── KMS_CSS/                   # 會員頁面樣式
│   │   ├── KMS_JS/                    # 會員頁面腳本
│   │   ├── KMS_Modals/                # 模態框
│   │   ├── KMS_Affiliate/             # 推薦系統
│   │   ├── KMS_Dashboard/             # 會員儀表板
│   │   ├── KMS_Orders/                # 訂單管理
│   │   ├── KMS_PCBuilder/             # PC 組裝工具
│   │   ├── KMS_Payment/               # 支付處理
│   │   └── KMS_Wallet/                # 錢包系統
│   └── KMS_Admin/                     # 管理員功能
│       ├── KMS_Dashboard/             # 管理員儀表板
│       ├── KMS_MemberManagement/      # 會員管理
│       ├── KMS_OrderManagement/       # 訂單管理
│       ├── KMS_PCManagement/          # PC 組件管理
│       ├── KMS_PriceManagement/       # 價格管理
│       ├── KMS_CreditManagement/      # 信用管理
│       ├── KMS_ChatManagement/        # 聊天管理
│       └── KMS_AffiliateManagement/   # 推薦管理
├── SQL/                               # 數據庫文件
│   ├── complete_database_setup.sql    # 完整數據庫設置
│   └── insert_default_users.sql       # 默認用戶插入
├── Favicon/                           # 網站圖標
├── KMS_VIP_PC/                        # VIP PC 圖片
└── lang/                              # 語言文件
```

## 數據庫結構

### 主要數據表

#### 1. users - 用戶表
存儲所有用戶的基本信息和認證數據。

**字段說明：**
- `id`: 用戶唯一標識符
- `username`: 用戶名（唯一）
- `password`: 加密後的密碼
- `email`: 電子郵件地址（唯一）
- `first_name`, `last_name`: 姓名
- `nickname`: 暱稱
- `gender`: 性別 (male/female/other/prefer_not_to_say)
- `birthday`: 生日
- `language`: 語言偏好 (en/zh-CN)
- `phone_number`: 電話號碼
- `street_address`, `city`, `state`, `zip_code`: 地址信息
- `is_verified`: 是否已驗證
- `is_active`: 是否活躍
- `is_admin`: 是否為管理員
- `created_at`, `updated_at`: 創建和更新時間
- `last_login`, `last_seen`: 最後登錄和在線時間

#### 2. user_wallets - 用戶錢包表
管理每個用戶的 KMS Credit 餘額。

**字段說明：**
- `user_id`: 關聯用戶ID
- `balance`: 可用餘額
- `frozen_balance`: 凍結餘額（待處理交易）
- `commission_balance`: 推薦佣金餘額
- `total_deposited`: 總充值金額
- `total_spent`: 總消費金額
- `total_commissions`: 總佣金收入

#### 3. credit_transactions - 信用交易記錄表
記錄所有 KMS Credit 相關的交易。

**交易類型：**
- `deposit`: 充值
- `withdraw`: 提現
- `spend`: 消費
- `refund`: 退款
- `admin_gift`: 管理員贈送
- `admin_deduct`: 管理員扣除
- `affiliate`: 推薦佣金

#### 4. orders - 服務訂單表
存儲照片/視頻優化和打印服務的訂單。

#### 5. pc_orders - PC 組裝訂單表 (已升級 - API 兼容)
存儲 PC 組裝相關的訂單，支持完整的訂單生命週期管理和 API 集成。

**字段說明：**
- `id`: 訂單唯一標識符
- `order_number`: 唯一訂單編號（格式：PC + 日期 + 序號，如 PC202501180001）
- `user_id`: 關聯用戶ID
- `order_type`: 訂單類型 (custom/prebuilt)
- `config_id`: 預設配置ID（向後兼容）
- `prebuilt_config_id`: 預設配置ID（新版本）
- `components`: JSON格式的選定組件和數量
- `configuration`: JSON格式的完整配置信息
- `total_amount`: 總金額（向後兼容）
- `estimated_price`: 預估價格
- `final_price`: 最終價格
- `admin_adjusted_price`: 管理員調整價格
- `admin_adjustment_reason`: 價格調整原因
- `status`: 訂單狀態 (pending/quoted/confirmed/processing/completed/cancelled/refunded)
- `payment_status`: 支付狀態 (pending/paid/failed/refunded)
- `payment_method`: 支付方式
- `shipping_address`: 配送地址
- `notes`: 客戶備註
- `admin_notes`: 管理員備註
- `estimated_completion_date`: 預計完成日期
- `created_at`, `updated_at`: 創建和更新時間

**新增功能：**
- **自動訂單編號生成**: 系統自動為每個訂單生成唯一的訂單編號
- **多層價格管理**: 支持預估價格、最終價格和管理員調整價格
- **擴展訂單狀態**: 新增 'quoted' 和 'completed' 狀態，支持完整的訂單流程
- **預計完成日期**: 支持設定和追蹤訂單完成時間
- **雙重配置支持**: 同時支持舊版和新版預設配置引用
- **API 兼容性**: 優化的數據結構支持 API 集成和外部系統對接

**訂單狀態流程：**
1. `pending`: 待處理（初始狀態）
2. `quoted`: 已報價（提供價格估算）
3. `confirmed`: 已確認（客戶確認訂單）
4. `processing`: 處理中（開始組裝）
5. `completed`: 已完成（組裝完成）
6. `cancelled`: 已取消
7. `refunded`: 已退款

#### 6. affiliate_codes - 推薦代碼表
管理用戶的推薦代碼。

#### 7. affiliate_referrals - 推薦記錄表
記錄推薦關係。

#### 8. affiliate_commissions - 推薦佣金表
管理推薦佣金的發放。

#### 9. service_prices - 服務價格表
存儲所有服務的動態定價。

**服務類別：**
- `optimize`: 照片/視頻優化服務
- `print`: 打印服務

#### 10. pc_component_categories - PC 組件類別表
存儲 PC 組件的分類信息，包括 CPU、GPU、RAM、Storage、PSU、Case、OS 等類別。

**字段說明：**
- `id`: 類別唯一標識符
- `category_name`: 類別名稱
- `category_name_en`: 英文類別名稱
- `category_name_zh`: 中文類別名稱
- `description`: 類別描述
- `is_active`: 是否啟用
- `sort_order`: 顯示順序

#### 11. pc_components - PC 組件表
存儲具體的 PC 組件信息和價格，關聯到組件類別。

**字段說明：**
- `id`: 組件唯一標識符
- `category_id`: 關聯的組件類別ID
- `component_name`: 組件名稱（多語言支持）
- `brand`: 品牌
- `model`: 型號
- `specifications`: JSON格式的詳細規格
- `base_price`: 基礎價格
- `current_price`: 當前價格
- `stock_quantity`: 庫存數量
- `description`: 組件描述（多語言支持）
- `image_url`: 組件圖片URL
- `is_active`: 是否啟用
- `sort_order`: 顯示順序

#### 12. pc_prebuilt_configs - 預設 PC 配置表
存儲預設的 PC 配置方案。

#### 12. chat_sessions & chat_messages - 聊天系統表
管理即時聊天功能。

#### 13. homepage_logos - 首頁標誌管理表
管理首頁左右兩側的標誌圖片，支持動態更換和管理。

**字段說明：**
- `id`: 標誌記錄唯一標識符
- `position`: 標誌位置 (left/right)
- `logo_path`: 標誌文件路徑
- `original_filename`: 原始文件名
- `is_active`: 是否啟用（每個位置只能有一個啟用的標誌）
- `uploaded_by`: 上傳者用戶ID
- `created_at`, `updated_at`: 創建和更新時間

**特性：**
- 支持左右兩個位置的獨立管理
- 唯一約束確保每個位置只有一個啟用的標誌
- 默認使用 KMS Logo (512x512px)
- 支持管理員動態更換標誌

#### 14. 頁面瀏覽追蹤系統 (Page Views System)

系統採用三層架構的頁面瀏覽追蹤系統，提供準確的訪問統計和防垃圾流量保護：

##### page_views - 詳細瀏覽記錄表
記錄每次頁面訪問的詳細信息，具備先進的防重複機制。

**字段說明：**
- `id`: 記錄唯一標識符
- `page_name`: 頁面名稱（默認為 'index'）
- `user_id`: 關聯用戶ID（可為空，支持匿名訪問）
- `ip_address`: 訪問者IP地址
- `user_agent_hash`: 用戶代理的SHA256哈希值（保護隱私）
- `session_id`: 會話標識符
- `created_at`: 訪問時間

**防垃圾流量機制：**
- 基於 IP 地址 + 用戶代理哈希 + 時間窗口（1分鐘）的重複檢測
- 防止頁面刷新、機器人和惡意流量造成的虛假統計
- 支持已登入和匿名用戶的準確區分
- 1分鐘冷卻期：同一訪客在1分鐘內的重複訪問只計算一次

**高性能索引設計：**
- `idx_page_name`: 快速查詢特定頁面的訪問記錄
- `idx_ip_address`: 分析訪問來源和地理分佈
- `idx_user_agent_hash`: 識別不同設備/瀏覽器類型
- `idx_created_at`: 按時間範圍進行統計分析
- `idx_user_id`: 追蹤已登入用戶的瀏覽行為
- `idx_duplicate_check`: 複合索引用於高效的重複訪問檢測（page_name, ip_address, user_agent_hash, created_at）

#### 15. page_view_summary - 頁面瀏覽統計摘要表
提供快速的頁面訪問統計數據，避免實時計算大量記錄。支持自動觸發器更新。

**字段說明：**
- `page_name`: 頁面名稱（唯一）
- `total_views`: 總瀏覽次數
- `unique_visitors`: 獨立訪客數（基於 IP + 用戶代理組合）
- `last_updated`: 最後更新時間（自動更新）

**自動更新機制：**
- **數據庫觸發器**: `update_page_view_summary_after_insert` 觸發器在新增頁面瀏覽記錄時自動更新統計摘要
- **實時統計**: 每次新增瀏覽記錄時自動計算總瀏覽次數和獨立訪客數
- **自動創建**: 如果頁面統計記錄不存在，觸發器會自動創建新記錄
- **高性能**: 避免實時查詢大量 page_views 記錄，提供快速的統計數據訪問

## 安裝和配置

### 1. 數據庫設置

1. 創建 MySQL 數據庫：
```sql
CREATE DATABASE kelvinkms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 執行完整數據庫設置：
```bash
mysql -u username -p kelvinkms < SQL/complete_database_setup.sql
```

**注意**: 數據庫設置腳本包含自動遷移功能，會為現有的 PC 訂單自動生成訂單編號。

3. 插入默認用戶：
```bash
mysql -u username -p kelvinkms < SQL/insert_default_users.sql
```

4. 設置頁面瀏覽追蹤系統：
```bash
mysql -u username -p kelvinkms < SQL/fix_page_views_tables.sql
```

或者運行 PHP 修復腳本：
```bash
php fix_page_views_system.php
```

### 2. 配置文件設置

編輯 `KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php`：

```php
// 數據庫配置
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'your_username');
define('DB_PASSWORD', 'your_password');
define('DB_NAME', 'kelvinkms');

// 其他配置...
```

### 3. 文件權限設置

確保以下目錄具有寫入權限：
- `logs/`
- `KMS_Apps/KMS_Member/KMS_Affiliate/logs/`

## 默認用戶賬戶

系統預設了兩個用戶賬戶：

### 管理員賬戶
- **用戶名**: KMSAdmin
- **密碼**: k1e9l9v9in
- **權限**: 完整管理員權限
- **初始餘額**: $1000.00

### 測試會員賬戶
- **用戶名**: KelvinKMS
- **密碼**: k1e9l9v9in
- **權限**: 普通會員
- **初始餘額**: $100.00

## 主要功能說明

### 1. KMS Credit 系統

KMS Credit 是網站的虛擬貨幣系統，用戶可以：
- 通過多種支付方式充值
- 用於購買各種服務
- 提現到外部賬戶
- 通過推薦獲得佣金

**支持的支付方式：**
- PayPal
- Stripe (信用卡/借記卡)
- Square
- Venmo
- Zelle

### 2. 服務系統

#### 照片/視頻優化服務
- 照片優化：$3.00/張
- 照片去水印：$3.00/張
- 視頻優化：$2.00/分鐘
- 視頻去水印（簡單）：$10.00/30分鐘內
- 視頻去水印（困難）：$30.00/30分鐘內

#### 打印服務
- 多種紙張類型和規格
- 黑白和彩色打印選項
- 照片紙打印
- 裝裱服務
- 相冊製作

### 3. PC 組裝服務

提供三種模式：
- **簡單模式**: 基於用途和預算的快速選擇
- **詳細模式**: 自定義選擇每個組件
- **預設配置**: 專業推薦的完整配置

**支持的組件類別：**
- CPU (Intel 285K, 265K, AMD 7800X3D, 9800X3D, 9950X3D)
- 內存 (DDR5 32-128GB)
- 存儲 (NVMe SSD 2-8TB)
- 顯卡 (RTX 5070-5090)
- 電源 (850-1200W)
- 機箱（小/中/大型）
- Windows 11 (Home/Pro)

### 4. 推薦系統

- 每個用戶都有唯一的推薦代碼
- 成功推薦可獲得 $50 佣金
- 佣金需要管理員審核後發放
- 佣金可轉換為 KMS Credit 或提現

### 5. 管理員功能

管理員可以：
- 管理所有用戶賬戶
- 添加/編輯/刪除會員
- 調整用戶 KMS Credit 餘額
- 管理服務價格
- 處理訂單
- 管理 PC 組件和價格
- 審核推薦佣金
- 查看系統統計

## 技術特性

### 安全特性
- 密碼哈希加密
- SQL 注入防護
- XSS 防護
- CSRF 保護
- 會話管理
- 輸入驗證

### 響應式設計
- 支持桌面和移動設備
- 自適應佈局
- 觸摸友好的界面

### 多語言支持
- 英語 (en)
- 簡體中文 (zh-CN)
- 易於擴展其他語言

### 性能優化
- 數據庫索引優化
- 靜態資源分離
- 異步 JavaScript 加載
- 緩存機制

### API 兼容性和集成
- **訂單管理 API**: PC 訂單系統支持 API 集成，便於外部系統對接
- **自動訂單編號**: 系統自動生成唯一的訂單追蹤編號
- **多層價格管理**: 支持預估、最終和調整價格的完整價格管理流程
- **擴展狀態管理**: 完整的訂單生命週期狀態追蹤
- **JSON 配置存儲**: 靈活的組件和配置信息存儲格式

## 維護和故障排除

### 常見問題

1. **數據庫連接失敗**
   - 檢查數據庫配置
   - 確認數據庫服務運行
   - 驗證用戶權限

2. **文件上傳失敗**
   - 檢查文件權限
   - 確認上傳目錄存在
   - 檢查 PHP 上傳限制

3. **支付處理問題**
   - 驗證支付 API 配置
   - 檢查網絡連接
   - 查看錯誤日誌

### 日誌文件位置
- 系統日誌：`logs/`
- 推薦系統日誌：`KMS_Apps/KMS_Member/KMS_Affiliate/logs/`
- PHP 錯誤日誌：服務器配置位置

### 備份建議
- 定期備份數據庫
- 備份上傳的文件
- 保存配置文件副本

## 更新和升級

### 版本控制
- 使用 Git 進行版本控制
- 標記重要版本
- 維護更新日誌

### 升級步驟
1. 備份當前系統
2. 測試新版本
3. 更新數據庫結構
4. 部署新代碼
5. 驗證功能正常

## 支持和聯繫

如需技術支持或有任何問題，請通過以下方式聯繫：
- 網站即時聊天系統
- 電子郵件：<EMAIL>
- 系統管理員：KMSAdmin

---

**最後更新**: 2025-07-18
**版本**: 1.1.0 - PC 訂單系統升級
**作者**: KelvinKMS 開發團隊

## 更新日誌

### v1.1.0 (2025-07-18)
- **PC 訂單系統重大升級**:
  - 新增自動訂單編號生成功能
  - 擴展訂單狀態管理（新增 quoted 和 completed 狀態）
  - 實現多層價格管理系統（預估/最終/調整價格）
  - 新增預計完成日期追蹤
  - 優化 API 兼容性和外部系統集成
  - 自動遷移現有訂單數據
