<?php
require_once __DIR__ . '/../KMS_Config/pc_system_constants.php';

class DatabaseResponseHelper {
    public static function success(array $data = [], string $message = 'Operation successful'): array {
        return [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    public static function error(string $message, array $context = []): array {
        return [
            'success' => false,
            'message' => $message,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    public static function validationError(array $errors): array {
        return [
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    public static function notFound(string $resource = 'Resource'): array {
        return [
            'success' => false,
            'message' => "$resource not found",
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    public static function unauthorized(string $message = 'Access denied'): array {
        return [
            'success' => false,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    public static function processOrderData(array $order): array {
        // Generate order_number if missing
        if (empty($order['order_number']) && isset($order['created_at'], $order['id'])) {
            $order['order_number'] = 'PC' . date('Ymd', strtotime($order['created_at'])) . sprintf('%04d', $order['id']);
        }
        
        // Handle JSON fields safely
        foreach (['configuration', 'components'] as $jsonField) {
            if (isset($order[$jsonField]) && $order[$jsonField]) {
                $decoded = json_decode($order[$jsonField], true);
                $order[$jsonField] = $decoded ?: [];
            } else {
                $order[$jsonField] = [];
            }
        }
        
        // Ensure all expected fields exist with default values
        $defaults = [
            'estimated_price' => null,
            'final_price' => null,
            'admin_adjusted_price' => null,
            'admin_adjustment_reason' => '',
            'estimated_completion_date' => null,
            'config_name' => null,
            'config_name_en' => null,
            'config_name_zh' => null,
            'first_name' => '',
            'last_name' => ''
        ];
        
        foreach ($defaults as $field => $defaultValue) {
            if (!isset($order[$field])) {
                $order[$field] = $defaultValue;
            }
        }
        
        return $order;
    }
    
    public static function outputJson(array $data): void {
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
    
    public static function validateOrderStatus(string $status): bool {
        return in_array($status, PCSystemConstants::ORDER_STATUSES, true);
    }
    
    public static function validatePaymentStatus(string $status): bool {
        return in_array($status, PCSystemConstants::PAYMENT_STATUSES, true);
    }
    
    public static function validateOrderType(string $type): bool {
        return in_array($type, PCSystemConstants::ORDER_TYPES, true);
    }
    
    /**
     * Validate required fields in an array
     */
    public static function validateRequiredFields(array $data, array $requiredFields): array {
        $errors = [];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "Field '$field' is required";
            }
        }
        return $errors;
    }
    
    /**
     * Sanitize string input for safe output
     */
    public static function sanitizeString(string $input): string {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}