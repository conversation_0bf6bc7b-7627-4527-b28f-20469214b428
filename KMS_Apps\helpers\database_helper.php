<?php
require_once 'error_handler.php';
require_once 'pc_system_constants.php';

class DatabaseHelper {
    private $connection;
    
    public function __construct() {
        $this->connection = get_db_connection();
        if (!$this->connection) {
            throw new DatabaseConnectionException('Failed to establish database connection: ' . mysqli_connect_error());
        }
    }
    
    public function tableExists(string $tableName): bool {
        $query = "SHOW TABLES LIKE ?";
        $result = $this->executeQuery($query, [$tableName]);
        return count($result) > 0;
    }
    
    public function columnExists(string $tableName, string $columnName): bool {
        $query = "SHOW COLUMNS FROM `$tableName` LIKE ?";
        try {
            $result = $this->executeQuery($query, [$columnName]);
            return count($result) > 0;
        } catch (DatabaseQueryException $e) {
            // Table doesn't exist
            return false;
        }
    }
    
    public function executeQuery(string $query, array $params = []): array {
        try {
            if (empty($params)) {
                $result = mysqli_query($this->connection, $query);
                if (!$result) {
                    throw new DatabaseQueryException(mysqli_error($this->connection), $query);
                }
            } else {
                $stmt = mysqli_prepare($this->connection, $query);
                if (!$stmt) {
                    throw new DatabaseQueryException('Prepare failed: ' . mysqli_error($this->connection), $query);
                }
                
                if (!empty($params)) {
                    $types = $this->inferTypes($params);
                    mysqli_stmt_bind_param($stmt, $types, ...$params);
                }
                
                if (!mysqli_stmt_execute($stmt)) {
                    throw new DatabaseQueryException('Execute failed: ' . mysqli_stmt_error($stmt), $query);
                }
                
                $result = mysqli_stmt_get_result($stmt);
                mysqli_stmt_close($stmt);
                
                if (!$result) {
                    throw new DatabaseQueryException('Get result failed: ' . mysqli_error($this->connection), $query);
                }
            }
            
            $data = [];
            while ($row = mysqli_fetch_assoc($result)) {
                $data[] = $row;
            }
            
            return $data;
            
        } catch (Exception $e) {
            if ($e instanceof DatabaseQueryException) {
                throw $e;
            }
            throw new DatabaseQueryException($e->getMessage(), $query);
        }
    }
    
    public function executeUpdate(string $query, array $params = []): int {
        try {
            $stmt = mysqli_prepare($this->connection, $query);
            if (!$stmt) {
                throw new DatabaseQueryException('Prepare failed: ' . mysqli_error($this->connection), $query);
            }
            
            if (!empty($params)) {
                $types = $this->inferTypes($params);
                mysqli_stmt_bind_param($stmt, $types, ...$params);
            }
            
            if (!mysqli_stmt_execute($stmt)) {
                throw new DatabaseQueryException('Execute failed: ' . mysqli_stmt_error($stmt), $query);
            }
            
            $affectedRows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
            
            return $affectedRows;
            
        } catch (Exception $e) {
            if ($e instanceof DatabaseQueryException) {
                throw $e;
            }
            throw new DatabaseQueryException($e->getMessage(), $query);
        }
    }
    
    private function inferTypes(array $params): string {
        $types = '';
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_float($param)) {
                $types .= 'd';
            } else {
                $types .= 's';
            }
        }
        return $types;
    }
    
    public function getLastInsertId(): int {
        return mysqli_insert_id($this->connection);
    }
    
    public function beginTransaction(): bool {
        return mysqli_begin_transaction($this->connection);
    }
    
    public function commit(): bool {
        return mysqli_commit($this->connection);
    }
    
    public function rollback(): bool {
        return mysqli_rollback($this->connection);
    }
    
    public function __destruct() {
        if ($this->connection) {
            close_db_connection($this->connection);
        }
    }
}