/**
 * Main JavaScript for KelvinKMS.com
 * Handles page navigation, slideshow, page view counter, and modal management
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

/**
 * Smoothly scrolls to a section by ID
 * @param {string} id - The ID of the target element
 */
function scrollToSection(id) {
    const element = document.getElementById(id);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    } else {
        console.warn(`Element with ID '${id}' not found`);
    }
}

/**
 * Manages automatic slideshow functionality with proper memory management
 * @class SlideshowManager
 */
class SlideshowManager {
    /**
     * Create a slideshow manager
     * @param {string} slideSelector - CSS selector for slide elements
     * @param {number} interval - Time between slides in milliseconds
     */
    constructor(slideSelector = '.slide', interval = 3000) {
        this.slideSelector = slideSelector;
        this.interval = interval;
        this.currentIndex = 0;
        this.intervalId = null;
        this.isRunning = false;
    }

    // Initialize and start the slideshow
    init() {
        const slides = this.getSlides();
        if (slides.length === 0) {
            console.warn('No slides found with selector:', this.slideSelector);
            return false;
        }

        this.showSlide(0);
        this.start();
        return true;
    }

    // Get all slide elements
    getSlides() {
        return document.querySelectorAll(this.slideSelector);
    }

    // Show a specific slide by index
    showSlide(index) {
        const slides = this.getSlides();
        if (slides.length === 0) return;

        // Hide all slides
        slides.forEach(slide => slide.style.display = 'none');

        // Show current slide
        this.currentIndex = index % slides.length;
        slides[this.currentIndex].style.display = 'block';
    }

    // Move to next slide
    nextSlide() {
        this.showSlide(this.currentIndex + 1);
    }

    // Start automatic slideshow
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.intervalId = setInterval(() => {
            this.nextSlide();
        }, this.interval);
    }

    // Stop automatic slideshow
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isRunning = false;
    }

    // Restart slideshow
    restart() {
        this.stop();
        this.start();
    }
}

// Main application initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize slideshow
    const slideshow = new SlideshowManager('.slide', 3000);
    slideshow.init();
    // Configuration for API endpoints
    const API_CONFIG = {
        primary: 'debug_page_views.php?action=count',
        fallback: 'KMS_Apps/KMS_Index/KMS_UserTracking/KMS_PHP/KMS_get_view_count.php',
        timeout: 5000
    };

    // Utility function to fetch with timeout
    async function fetchWithTimeout(url, timeout = 5000) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, { signal: controller.signal });
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    // Utility function to update counter display
    function updateCounterDisplay(count, source = 'primary') {
        const counterElement = document.getElementById('counter');
        if (!counterElement) {
            console.warn('Counter element not found in DOM');
            return false;
        }
        
        counterElement.textContent = count;
        console.log(`Page views updated to: ${count} (source: ${source})`);
        return true;
    }

    // Main counter update function with proper error handling
    async function updatePageViewCounter() {
        try {
            // Try primary API first
            const primaryData = await fetchWithTimeout(API_CONFIG.primary, API_CONFIG.timeout);
            
            if (primaryData?.success && typeof primaryData.count === 'number') {
                updateCounterDisplay(primaryData.count, 'primary');
                return;
            }
            
            console.warn('Primary API returned invalid data:', primaryData);
            throw new Error('Invalid response from primary API');
            
        } catch (primaryError) {
            console.warn('Primary API failed:', primaryError.message);
            
            try {
                // Fallback to secondary API
                const fallbackData = await fetchWithTimeout(API_CONFIG.fallback, API_CONFIG.timeout);
                
                if (fallbackData && typeof fallbackData.count === 'number') {
                    updateCounterDisplay(fallbackData.count, 'fallback');
                    return;
                }
                
                throw new Error('Invalid response from fallback API');
                
            } catch (fallbackError) {
                console.error('Both APIs failed:', {
                    primary: primaryError.message,
                    fallback: fallbackError.message
                });
                
                // Set default value and notify user
                updateCounterDisplay(0, 'default');
            }
        }
    }

    // Try immediate update
    updatePageViewCounter();
    
    // Also try after a short delay to ensure DOM is fully ready (Chrome fallback)
    setTimeout(updatePageViewCounter, 100);

    // Modal management system
    class ModalManager {
        constructor() {
            this.activeModal = null;
            this.originalBodyOverflow = document.body.style.overflow;
        }

        // Open a modal by ID
        open(modalId) {
            this.close(); // Close any existing modal first
            
            const modal = document.getElementById(modalId);
            if (!modal) {
                console.warn(`Modal with ID '${modalId}' not found`);
                return false;
            }

            modal.style.display = 'block';
            this.activeModal = modal;
            this.disableBackgroundScroll();
            
            console.log(`Modal '${modalId}' opened`);
            return true;
        }

        // Close the currently active modal
        close() {
            if (this.activeModal) {
                this.activeModal.style.display = 'none';
                this.activeModal = null;
                this.enableBackgroundScroll();
                console.log('Modal closed');
            }
        }

        // Check if any modal is currently open
        isOpen() {
            return this.activeModal !== null;
        }

        // Private methods for scroll management
        disableBackgroundScroll() {
            document.body.style.overflow = 'hidden';
        }

        enableBackgroundScroll() {
            document.body.style.overflow = this.originalBodyOverflow || 'auto';
        }

        // Setup event listeners for a modal
        setupModal(modalId, triggerIds, closeIds) {
            const modal = document.getElementById(modalId);
            if (!modal) return;

            // Setup trigger buttons
            triggerIds.forEach(triggerId => {
                const trigger = document.getElementById(triggerId);
                if (trigger) {
                    trigger.addEventListener('click', () => this.open(modalId));
                }
            });

            // Setup close buttons
            closeIds.forEach(closeId => {
                const closeBtn = document.getElementById(closeId);
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => this.close());
                }
            });
        }
    }

    // Initialize modal manager
    const modalManager = new ModalManager();

    // Setup modals with their respective triggers and close buttons
    modalManager.setupModal('registerModal', ['registerBtn'], ['closeRegister', 'registerCloseBtn']);
    modalManager.setupModal('loginModal', ['loginBtn'], ['closeLogin', 'loginCloseBtn']);

    // Optional: Close modal when clicking outside (uncomment if needed)
    // window.addEventListener('click', (e) => {
    //     if (e.target.classList.contains('modal')) {
    //         modalManager.close();
    //     }
    // });

    // Remove the window click handler to prevent closing by clicking outside
    // window.onclick = e => {
    //     if (e.target === registerModal) registerModal.style.display = 'none';
    //     if (e.target === loginModal) loginModal.style.display = 'none';
    // };
});
